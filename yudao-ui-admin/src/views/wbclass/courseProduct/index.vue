<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="商品名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择商品状态"
          clearable
          size="small"
        >
          <el-option label="上架" :value="1" />
          <el-option label="下架" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['wbclass:course-product:create']"
          >新增商品</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['wbclass:course-product:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column
        label="商品封面"
        align="center"
        prop="coverUrl"
        width="100"
      >
        <template slot-scope="scope">
          <el-image
            v-if="scope.row.coverUrl"
            :src="scope.row.coverUrl"
            style="width: 60px; height: 40px"
            fit="cover"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="商品名称"
        align="center"
        prop="name"
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column
        label="商品描述"
        align="center"
        prop="description"
        min-width="200"
        show-overflow-tooltip
      />
      <el-table-column label="SKU信息" align="center" min-width="280">
        <template slot-scope="scope">
          <div v-if="scope.row.skus && scope.row.skus.length > 0">
            <div
              v-for="sku in scope.row.skus"
              :key="sku.id"
              style="margin-bottom: 4px"
            >
              <el-tag
                :type="sku.status === 1 ? 'success' : 'danger'"
                size="mini"
              >
                {{ sku.skuName }}
              </el-tag>
              <span style="margin-left: 8px; font-size: 12px">
                ¥{{ formatPrice(sku.salePrice) }}
              </span>
              <span
                style="
                  margin-left: 4px;
                  font-size: 12px;
                  color: #999;
                  text-decoration: line-through;
                "
              >
                ¥{{ formatPrice(sku.originalPrice) }}
              </span>
              <span style="margin-left: 8px; font-size: 12px; color: #409eff">
                库存:{{ sku.stock || 0 }}
              </span>
            </div>
          </div>
          <span v-else style="color: #999">暂无SKU</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? "上架" : "下架" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" width="80" />
      <el-table-column label="浏览/销量" align="center" width="100">
        <template slot-scope="scope">
          <div>{{ scope.row.viewCount || 0 }}</div>
          <div style="color: #67c23a">{{ scope.row.salesCount || 0 }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="220"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-operation"
            @click="handleManageSku(scope.row)"
            style="color: #409eff"
            >SKU管理</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['wbclass:course-product:update']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['wbclass:course-product:delete']"
            style="color: #f56c6c"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="900px"
      v-dialogDrag
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入商品名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">上架</el-radio>
                <el-radio :label="2">下架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="商品封面" prop="coverUrl">
          <el-input
            v-model="form.coverUrl"
            placeholder="请输入商品封面图片URL"
          />
          <div v-if="form.coverUrl" style="margin-top: 10px">
            <el-image
              :src="form.coverUrl"
              style="width: 200px; height: 120px"
              fit="cover"
            />
          </div>
        </el-form-item>

        <el-form-item label="商品描述" prop="description">
          <editor v-model="form.description" :min-height="200" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number
                v-model="form.sort"
                :min="0"
                placeholder="请输入排序值"
                style="width: 100%"
              />
              <div style="font-size: 12px; color: #999; margin-top: 5px">
                数值越小排序越靠前
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- SKU管理 -->
        <el-form-item label="SKU规格" prop="skus">
          <div
            style="border: 1px solid #dcdfe6; border-radius: 4px; padding: 16px"
          >
            <div style="margin-bottom: 16px">
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-plus"
                @click="addSku"
              >
                添加SKU
              </el-button>
            </div>

            <el-table :data="form.skus" style="width: 100%">
              <el-table-column label="排序" width="80" align="center">
                <template slot-scope="scope">
                  <div
                    style="
                      display: flex;
                      flex-direction: column;
                      align-items: center;
                      justify-content: center;
                      height: 100%;
                      min-height: 40px;
                    "
                  >
                    <el-button
                      v-if="scope.$index > 0"
                      type="text"
                      size="mini"
                      icon="el-icon-arrow-up"
                      @click="moveSkuUp(scope.$index)"
                      style="margin: 1px 0; padding: 2px 4px"
                    />
                    <el-button
                      v-if="scope.$index < form.skus.length - 1"
                      type="text"
                      size="mini"
                      icon="el-icon-arrow-down"
                      @click="moveSkuDown(scope.$index)"
                      style="margin: 1px 0; padding: 2px 4px"
                    />
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="SKU名称" width="150">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.skuName"
                    placeholder="如：标准版"
                    size="mini"
                  />
                </template>
              </el-table-column>
              <el-table-column label="原价(元)" width="120">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.originalPriceYuan"
                    placeholder="0.00"
                    size="mini"
                    type="number"
                    min="0"
                    step="0.01"
                  />
                </template>
              </el-table-column>
              <el-table-column label="售价(元)" width="120">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.salePriceYuan"
                    placeholder="0.00"
                    size="mini"
                    type="number"
                    min="0"
                    step="0.01"
                  />
                </template>
              </el-table-column>
              <el-table-column label="库存" width="100">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.stock"
                    placeholder="999"
                    size="mini"
                    type="number"
                    min="0"
                  />
                </template>
              </el-table-column>
              <el-table-column label="状态" width="100">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.status" size="mini">
                    <el-option label="上架" :value="1" />
                    <el-option label="下架" :value="2" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="规格信息" min-width="150">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.specInfo"
                    placeholder="JSON格式规格信息"
                    size="mini"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-delete"
                    @click="removeSku(scope.$index)"
                    style="color: #f56c6c"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <div
              v-if="!form.skus || form.skus.length === 0"
              style="text-align: center; color: #999; padding: 20px"
            >
              暂无SKU，请点击"添加SKU"按钮添加
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      title="商品详情"
      :visible.sync="viewOpen"
      width="800px"
      append-to-body
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="商品名称">{{
          viewData.name
        }}</el-descriptions-item>
        <el-descriptions-item label="商品状态">
          <el-tag :type="viewData.status === 1 ? 'success' : 'danger'">
            {{ viewData.status === 1 ? "上架" : "下架" }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="浏览次数">{{
          viewData.viewCount || 0
        }}</el-descriptions-item>
        <el-descriptions-item label="销售数量">{{
          viewData.salesCount || 0
        }}</el-descriptions-item>
        <el-descriptions-item label="排序">{{
          viewData.sort || 0
        }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{
          parseTime(viewData.createTime)
        }}</el-descriptions-item>
        <el-descriptions-item label="商品封面" :span="2">
          <el-image
            v-if="viewData.coverUrl"
            :src="viewData.coverUrl"
            style="width: 300px; height: 180px"
            fit="cover"
          />
          <span v-else>无封面</span>
        </el-descriptions-item>
        <el-descriptions-item label="商品描述" :span="2">
          <div
            v-html="viewData.description"
            style="max-height: 200px; overflow-y: auto"
          ></div>
        </el-descriptions-item>
        <el-descriptions-item label="SKU信息" :span="2">
          <el-table
            v-if="viewData.skus && viewData.skus.length > 0"
            :data="viewData.skus"
            style="width: 100%"
            size="mini"
          >
            <el-table-column prop="skuName" label="SKU名称" />
            <el-table-column label="原价">
              <template slot-scope="scope">
                ¥{{ formatPrice(scope.row.originalPrice) }}
              </template>
            </el-table-column>
            <el-table-column label="售价">
              <template slot-scope="scope">
                ¥{{ formatPrice(scope.row.salePrice) }}
              </template>
            </el-table-column>
            <el-table-column label="库存" prop="stock" />
            <el-table-column label="销量" prop="salesCount" />
            <el-table-column label="状态">
              <template slot-scope="scope">
                <el-tag
                  :type="scope.row.status === 1 ? 'success' : 'danger'"
                  size="mini"
                >
                  {{ scope.row.status === 1 ? "上架" : "下架" }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
          <span v-else style="color: #999">暂无SKU</span>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- SKU管理组件 -->
    <SkuManagement
      :visible.sync="skuManageOpen"
      :product-id="currentProductId"
      :available-courses="availableCourses"
      @close="handleSkuManageClose"
    />
  </div>
</template>

<script>
import {
  createWbclassCourseProduct,
  deleteWbclassCourseProduct,
  exportWbclassCourseProductExcel,
  getWbclassCoursePage,
  getWbclassCourseProduct,
  getWbclassCourseProductPage,
  updateWbclassCourseProduct,
} from "@/api/wbclass/courseProduct";
import Editor from "@/components/Editor";
import SkuManagement from "./components/SkuManagement.vue";

export default {
  name: "WbclassCourseProduct",
  components: {
    Editor,
    SkuManagement,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 课程商品列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看详情弹出层
      viewOpen: false,
      // 查看详情数据
      viewData: {},
      // 是否显示SKU管理弹出层
      skuManageOpen: false,
      // 当前管理的商品ID
      currentProductId: null,
      // 可选课程列表
      availableCourses: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        status: null,
        createTime: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "商品名称不能为空", trigger: "blur" },
        ],
        status: [
          { required: true, message: "商品状态不能为空", trigger: "change" },
        ],
        sort: [
          {
            type: "number",
            min: 0,
            message: "排序值必须大于等于0",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.loadAvailableCourses();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getWbclassCourseProductPage(this.queryParams).then((response) => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 价格格式化 */
    formatPrice(price) {
      if (!price || price === 0) return "0.00";
      return (price / 100).toFixed(2);
    },
    /** 添加SKU */
    addSku() {
      if (!this.form.skus) {
        this.form.skus = [];
      }
      this.form.skus.push({
        skuName: "",
        originalPriceYuan: "0.00",
        salePriceYuan: "0.00",
        stock: "999",
        status: 1,
        specInfo: "",
      });
    },
    /** 删除SKU */
    removeSku(index) {
      this.form.skus.splice(index, 1);
    },
    /** SKU上移 */
    moveSkuUp(index) {
      if (index > 0) {
        const temp = this.form.skus[index];
        this.$set(this.form.skus, index, this.form.skus[index - 1]);
        this.$set(this.form.skus, index - 1, temp);
      }
    },
    /** SKU下移 */
    moveSkuDown(index) {
      if (index < this.form.skus.length - 1) {
        const temp = this.form.skus[index];
        this.$set(this.form.skus, index, this.form.skus[index + 1]);
        this.$set(this.form.skus, index + 1, temp);
      }
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        coverUrl: undefined,
        description: undefined,
        status: 1,
        sort: 0,
        skus: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        name: null,
        status: null,
        createTime: [],
      };
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加课程商品";
    },
    /** 查看详情操作 */
    handleView(row) {
      this.viewData = { ...row };
      this.viewOpen = true;
    },
    /** SKU管理操作 */
    handleManageSku(row) {
      this.currentProductId = row.id;
      this.skuManageOpen = true;
    },
    /** 取消SKU管理 */
    handleSkuManageClose() {
      this.skuManageOpen = false;
    },
    /** 加载可选课程列表 */
    async loadAvailableCourses() {
      try {
        const response = await getWbclassCoursePage({
          pageNo: 1,
          pageSize: 1000,
        });
        this.availableCourses = response.data.list || [];
      } catch (error) {
        console.error("加载课程列表失败:", error);
        // 如果API调用失败，使用模拟数据
        this.availableCourses = [
          {
            id: 1,
            name: "基础英语课程",
            description: "适合初学者的英语基础课程",
            status: 1,
          },
          {
            id: 2,
            name: "高级英语课程",
            description: "高级英语语法和写作课程",
            status: 1,
          },
          {
            id: 3,
            name: "商务英语课程",
            description: "职场商务英语专项训练",
            status: 2,
          },
        ];
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getWbclassCourseProduct(id).then((response) => {
        let data = response.data;

        // 处理SKU数据
        if (data.skus && data.skus.length > 0) {
          data.skus = data.skus.map((sku) => ({
            ...sku,
            originalPriceYuan: sku.originalPrice
              ? (sku.originalPrice / 100).toFixed(2)
              : "0.00",
            salePriceYuan: sku.salePrice
              ? (sku.salePrice / 100).toFixed(2)
              : "0.00",
            stock: sku.stock ? sku.stock.toString() : "999",
          }));
        } else {
          data.skus = [];
        }

        this.form = data;
        this.open = true;
        this.title = "修改课程商品";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }

        // 处理表单数据
        let formData = { ...this.form };

        // 处理SKU数据
        if (formData.skus && formData.skus.length > 0) {
          formData.skus = formData.skus.map((sku) => ({
            id: sku.id, // 保留ID，用于判断是更新还是插入
            productId: sku.productId,
            skuName: sku.skuName,
            skuCode: sku.skuCode,
            originalPrice: sku.originalPriceYuan
              ? Math.round(parseFloat(sku.originalPriceYuan) * 100)
              : 0,
            salePrice: sku.salePriceYuan
              ? Math.round(parseFloat(sku.salePriceYuan) * 100)
              : 0,
            stock: sku.stock ? parseInt(sku.stock) : 999,
            salesCount: sku.salesCount,
            sort: sku.sort,
            status: sku.status,
            specInfo: sku.specInfo,
            courseIds: sku.courses ? sku.courses.map((c) => c.id) : [], // 添加courseIds字段
          }));
        }

        // 修改的提交
        if (formData.id != null) {
          updateWbclassCourseProduct(formData).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createWbclassCourseProduct(formData).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$modal
        .confirm('是否确认删除商品"' + row.name + '"?')
        .then(function () {
          return deleteWbclassCourseProduct(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      let params = { ...this.queryParams };
      params.pageNo = undefined;
      params.pageSize = undefined;

      this.$modal
        .confirm("是否确认导出所有课程商品数据?")
        .then(() => {
          this.exportLoading = true;
          return exportWbclassCourseProductExcel(params);
        })
        .then((response) => {
          this.$download.excel(response, "课程商品.xls");
          this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
