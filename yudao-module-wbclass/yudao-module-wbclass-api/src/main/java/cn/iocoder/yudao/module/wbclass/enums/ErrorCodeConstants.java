package cn.iocoder.yudao.module.wbclass.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * 错误码枚举类
 *
 * <AUTHOR>
 */
public interface ErrorCodeConstants {

    // ========== 作业相关 1-001-000-000 ==========
    ErrorCode HOMEWORK_MAIN_NOT_EXISTS = new ErrorCode(1_001_000_001, "作业不存在");
    ErrorCode HOMEWORK_ASSIGNEE_NOT_EXISTS = new ErrorCode(1_001_000_002, "作业分配记录不存在");
    ErrorCode HOMEWORK_SUBMISSION_NOT_EXISTS = new ErrorCode(1_001_000_003, "作业提交记录不存在");
    ErrorCode HOMEWORK_NOT_SUBMITTED = new ErrorCode(1_001_000_004, "作业未提交");
    ErrorCode HOMEWORK_NOT_GRADED = new ErrorCode(1_001_000_005, "作业未批改");
    ErrorCode HOMEWORK_GRADE_INVALID = new ErrorCode(1_001_000_006, "无效的评级");
    ErrorCode HOMEWORK_GRADE_PERMISSION_DENIED = new ErrorCode(1_001_000_007, "无权限批改作业");
    ErrorCode HOMEWORK_SUBMISSION_NOT_SELF = new ErrorCode(1_001_000_008, "不能回复他人的作业");
    ErrorCode HOMEWORK_FEEDBACK_NOT_EXISTS = new ErrorCode(1_001_000_009, "作业评价不存在");
    ErrorCode HOMEWORK_FEEDBACK_DELETE_DENIED = new ErrorCode(1_001_000_010, "无权删除该评价");
    ErrorCode HOMEWORK_SUBMISSIONS_NOT_EXISTS = new ErrorCode(1_001_000_011, "作业提交记录不存在");
    ErrorCode HOMEWORK_SUBMISSION_IMAGE_NOT_EXISTS = new ErrorCode(1_001_000_012, "原始图片不存在");
    ErrorCode HOMEWORK_SUBMISSIONS_HAD_GRADING = new ErrorCode(1_001_000_013, "作业已批改，无法再次批改");
    ErrorCode HOMEWORK_MAIN_NOT_FOUND = new ErrorCode(1_001_000_014, "作业主表记录不存在");
    ErrorCode HOMEWORK_NO_PERMISSION = new ErrorCode(1_001_000_015, "您没有权限操作该作业");

    // ========== 班级相关 1-002-000-000 ==========
    ErrorCode WBCLASS_NOT_EXISTS = new ErrorCode(1_002_000_001, "班级不存在");
    ErrorCode WBCLASS_NAME_DUPLICATE = new ErrorCode(1_002_000_002, "班级名称已存在");
    ErrorCode WBCLASS_MEMBER_NOT_EXISTS = new ErrorCode(1_002_000_003, "班级成员不存在");
    ErrorCode WBCLASS_MEMBER_EXISTS = new ErrorCode(1_002_000_004, "该成员已在班级中");
    ErrorCode WBCLASS_TEACHER_EXISTS = new ErrorCode(1_002_000_005, "班级已有班主任");
    ErrorCode WBCLASS_TEACHER_NOT_EXISTS = new ErrorCode(1_002_000_006, "班级没有班主任");
    ErrorCode WBCLASS_STATUS_CLOSED = new ErrorCode(1_002_000_007, "班级已关闭");

    ErrorCode WBCLASS_HOMEWORK_ASSIGNEES_NOT_EXISTS = new ErrorCode(1010001, "作业定向发布表，用于精确定位到学生不存在");

    ErrorCode WBCLASS_HOMEWORK_SUBMISSIONS_NOT_EXISTS = new ErrorCode(1011001, "学生提交记录不存在");

    // 用户相关错误码
    ErrorCode USER_NOT_EXISTS = new ErrorCode(1004001000, "用户不存在");
    ErrorCode USER_NOT_STUDENT = new ErrorCode(1004001001, "该用户不是学生");

    // ========== 作业广场相关 1008004000 ==========
    ErrorCode HOMEWORK_NOT_EXISTS = new ErrorCode(1008004000, "作业不存在");
    ErrorCode HOMEWORK_ALREADY_RECEIVED = new ErrorCode(1008004001, "已领取该作业");

    ErrorCode CLASS_NOT_EXISTS = new ErrorCode(1002004000, "班级不存在");

    ErrorCode NO_PERMISSION_TO_OPERATE_CLASS = new ErrorCode(1002004001, "您没有权限操作该班级");
    ErrorCode HOMEWORK_EXERCISE_NOT_EXISTS = new ErrorCode(1_001_000_001, "作业练习项目不存在, id:{}");

    ErrorCode UNIT_LESSON_HOMEWORK_NOT_EXISTS = new ErrorCode(1_002_000_001, "作业练习不存在");

    // ========== 作业提交相关 ==========
    ErrorCode HOMEWORK_SUBMISSION_DETAIL_NOT_EXISTS = new ErrorCode(1002006003, "作业提交明细不存在");

    ErrorCode WORDS_NOT_EXISTS = new ErrorCode(1003006003, "单词不存在");
    ErrorCode WBCLASS_WORDS_IMPORT_LIST_IS_EMPTY = new ErrorCode(1003006004, "导入的单词列表为空");


    // ========== 单词评分 1-003-004-000 ==========
    ErrorCode WORD_EVALUATION_FAILED = new ErrorCode(1003004000, "单词评分失败");
    ErrorCode ARTICLE_EVALUATION_FAILED = new ErrorCode(1003004001, "文章跟读评分失败");

    ErrorCode OCR_RECOGNITION_FAILED = new ErrorCode(1002006000, "OCR识别失败");
    ErrorCode HOMEWORK_EXERCISE_WORD_ANSWERS_EMPTY = new ErrorCode(1002004015, "练习的单词列表为空");
    ErrorCode HOMEWORK_EXERCISE_CATEGORY_ERROR = new ErrorCode(1002004016, "练习类型错误");
    ErrorCode OCR_DAILY_LIMIT_EXCEEDED = new ErrorCode(1002004017, "今天默写已经提交过");

    ErrorCode ARTICLES_NOT_EXISTS = new ErrorCode(1002004018, "文章不存在");

    // ========== 课程商城相关 1-005-000-000 ==========
    ErrorCode COURSE_NOT_EXISTS = new ErrorCode(1_005_000_001, "课程不存在");
    ErrorCode COURSE_ORDER_NOT_EXISTS = new ErrorCode(1_005_000_002, "课程订单不存在");
    ErrorCode COURSE_ALREADY_PURCHASED = new ErrorCode(1_005_000_003, "课程已购买");
    ErrorCode COURSE_ORDER_ALREADY_PAID = new ErrorCode(1_005_000_004, "订单已支付");
    ErrorCode COURSE_ORDER_ALREADY_CANCELLED = new ErrorCode(1_005_000_005, "订单已取消");
    ErrorCode COURSE_ORDER_STATUS_NOT_SUPPORT_REFUND = new ErrorCode(1_005_000_006, "订单状态不支持退款");
    ErrorCode COURSE_ORDER_NOT_PAID = new ErrorCode(1_005_000_007, "订单未支付");
    ErrorCode COURSE_ORDER_ALREADY_REFUNDED = new ErrorCode(1_005_000_008, "订单已退款");
    ErrorCode COURSE_ORDER_REFUND_PRICE_EXCEED = new ErrorCode(1_005_000_009, "退款金额不能超过实付金额");
    ErrorCode COURSE_ORDER_PARAM_ERROR = new ErrorCode(1_005_000_018, "订单参数错误");
    ErrorCode COURSE_SKU_NOT_EXISTS = new ErrorCode(1_005_000_010, "课程SKU不存在");
    ErrorCode COURSE_SKU_NOT_AVAILABLE = new ErrorCode(1_005_000_011, "课程SKU不可购买");
    ErrorCode COURSE_SKU_REQUIRED = new ErrorCode(1_005_000_012, "课程必须至少有一个SKU");
    ErrorCode COURSE_PRODUCT_NOT_EXISTS = new ErrorCode(1_005_000_013, "课程商品不存在");

    // ========== 课节相关 1-006-000-000 ==========
    ErrorCode COURSE_LESSON_NOT_EXISTS = new ErrorCode(1_006_000_001, "课节不存在");
    ErrorCode COURSE_LESSON_EXERCISE_NOT_EXISTS = new ErrorCode(1_006_000_002, "课节练习不存在");

    // ========== 课程商品相关错误码 ==========
    ErrorCode COURSE_PRODUCT_SKU_NOT_EXISTS = new ErrorCode(1_018_005_001, "课程商品SKU不存在");
    ErrorCode PRODUCT_SKU_NOT_EXISTS = new ErrorCode(1_005_000_014, "商品SKU不存在");
    ErrorCode PRODUCT_SKU_NOT_MATCH_PRODUCT = new ErrorCode(1_005_000_015, "商品SKU与商品不匹配");
    ErrorCode SKU_NO_COURSE_RELATION = new ErrorCode(1_005_000_016, "SKU未关联任何课程");
    ErrorCode PRODUCT_SKU_ALREADY_PURCHASED = new ErrorCode(1_005_000_017, "商品SKU已购买");

    // ========== 课程用户关联相关 1-007-000-000 ==========
    ErrorCode COURSE_USER_RELATION_COURSE_NOT_EXISTS = new ErrorCode(1_007_000_001, "课程不存在");
    ErrorCode COURSE_USER_RELATION_ACQUIRE_TYPE_NOT_SUPPORTED = new ErrorCode(1_007_000_002, "不支持的获得方式");
}
