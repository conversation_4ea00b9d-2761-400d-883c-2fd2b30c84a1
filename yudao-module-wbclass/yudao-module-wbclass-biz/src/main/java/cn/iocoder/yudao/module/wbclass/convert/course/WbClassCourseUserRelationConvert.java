package cn.iocoder.yudao.module.wbclass.convert.course;

import cn.iocoder.yudao.module.wbclass.controller.admin.vo.course.WbClassCourseUserRelationRespVO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseUserRelationDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 课程用户关联 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface WbClassCourseUserRelationConvert {

    WbClassCourseUserRelationConvert INSTANCE = Mappers.getMapper(WbClassCourseUserRelationConvert.class);

    WbClassCourseUserRelationRespVO convert(WbClassCourseUserRelationDO bean);

    List<WbClassCourseUserRelationRespVO> convertList(List<WbClassCourseUserRelationDO> list);

}
