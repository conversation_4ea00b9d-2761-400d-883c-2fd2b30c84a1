package cn.iocoder.yudao.module.wbclass.enums;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 课程获得方式枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CourseAcquireTypeEnum implements IntArrayValuable {

    PURCHASE(1, "购买"),
    GIFT(2, "赠送"),
    ACTIVITY(3, "活动"),
    ADMIN_ASSIGN(4, "管理员分配"),
    OTHER(5, "其他");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(CourseAcquireTypeEnum::getType).toArray();

    /**
     * 获得方式
     */
    private final Integer type;
    /**
     * 获得方式名
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static CourseAcquireTypeEnum valueOf(Integer type) {
        return Arrays.stream(values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
    }

}
