package cn.iocoder.yudao.module.wbclass.controller.admin.vo.course;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 课程用户关联创建 Request VO
 *
 * <AUTHOR>
 */
@ApiModel("管理后台 - 课程用户关联创建 Request VO")
@Data
public class WbClassCourseUserRelationCreateReqVO {

    @ApiModelProperty(value = "用户ID", required = true, example = "1024")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "课程ID", required = true, example = "1024")
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    @ApiModelProperty(value = "获得方式：2-赠送，3-活动，4-管理员分配", required = true, example = "2")
    @NotNull(message = "获得方式不能为空")
    private Integer acquireType;

    @ApiModelProperty(value = "过期时间（NULL表示永久有效）", example = "2024-12-31 23:59:59")
    private LocalDateTime expireTime;

    @ApiModelProperty(value = "备注信息", example = "管理员手动分配")
    private String remark;

}
