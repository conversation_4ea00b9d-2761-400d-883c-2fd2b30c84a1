package cn.iocoder.yudao.module.wbclass.service.course;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseOrderDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassOrderCourseRelationDO;
import cn.iocoder.yudao.module.wbclass.dal.mysql.course.WbClassCourseMapper;
import cn.iocoder.yudao.module.wbclass.dal.mysql.course.WbClassCourseOrderMapper;
import cn.iocoder.yudao.module.wbclass.dal.mysql.course.WbClassOrderCourseRelationMapper;
import cn.iocoder.yudao.module.wbclass.enums.PayStatusEnum;
import cn.iocoder.yudao.module.wbclass.enums.RefundStatusEnum;
import cn.iocoder.yudao.module.wbclass.service.product.WbClassCourseProductSkuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单课程关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WbClassOrderCourseRelationServiceImpl implements WbClassOrderCourseRelationService {

    @Resource
    private WbClassOrderCourseRelationMapper relationMapper;

    @Resource
    private WbClassCourseMapper courseMapper;
    @Resource
    private WbClassCourseUserRelationService courseUserRelationService;

    @Resource
    private WbClassCourseOrderMapper orderMapper;

    @Resource
    private WbClassCourseProductSkuService skuService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrderCourseRelations(Long orderId, Long skuId) {
        if (skuId == null) {
            log.warn("SKU ID为空，无法创建订单课程关联关系: orderId={}", orderId);
            return;
        }

        // 获取SKU关联的课程列表
        List<WbClassCourseDO> courses = skuService.getCoursesBySkuId(skuId);
        if (CollUtil.isEmpty(courses)) {
            log.warn("SKU没有关联课程，无法创建订单课程关联关系: orderId={}, skuId={}", orderId, skuId);
            return;
        }

        createOrderCourseRelations(orderId, courses);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrderCourseRelation(Long orderId, Long courseId, String courseName, Integer sort) {
        // 检查是否已存在关联关系
        if (relationMapper.existsByOrderIdAndCourseId(orderId, courseId)) {
            log.info("订单课程关联关系已存在，跳过创建: orderId={}, courseId={}", orderId, courseId);
            return;
        }

        WbClassOrderCourseRelationDO relation = WbClassOrderCourseRelationDO.builder()
                .orderId(orderId)
                .courseId(courseId)
                .courseName(courseName)
                .sort(sort != null ? sort : 0)
                .status(1) // 1-正常
                .build();

        relationMapper.insert(relation);
        log.info("创建订单课程关联关系成功: orderId={}, courseId={}", orderId, courseId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrderCourseRelations(Long orderId, List<WbClassCourseDO> courses) {
        if (CollUtil.isEmpty(courses)) {
            log.warn("课程列表为空，无法创建订单课程关联关系: orderId={}", orderId);
            return;
        }

        for (int i = 0; i < courses.size(); i++) {
            WbClassCourseDO course = courses.get(i);
            createOrderCourseRelation(orderId, course.getId(), course.getName(), i);
        }

        log.info("批量创建订单课程关联关系成功: orderId={}, 课程数量={}", orderId, courses.size());
    }

    @Override
    public List<WbClassCourseDO> getCoursesByOrderId(Long orderId) {
        List<WbClassOrderCourseRelationDO> relations = relationMapper.selectListByOrderId(orderId);
        if (CollUtil.isEmpty(relations)) {
            return new ArrayList<>();
        }

        List<Long> courseIds = relations.stream()
                .map(WbClassOrderCourseRelationDO::getCourseId)
                .collect(Collectors.toList());

        return courseMapper.selectBatchIds(courseIds);
    }

    @Override
    public List<WbClassCourseDO> getCoursesByOrderIds(Collection<Long> orderIds) {
        if (CollUtil.isEmpty(orderIds)) {
            return new ArrayList<>();
        }

        List<WbClassOrderCourseRelationDO> relations = relationMapper.selectListByOrderIds(orderIds);
        if (CollUtil.isEmpty(relations)) {
            return new ArrayList<>();
        }

        List<Long> courseIds = relations.stream()
                .map(WbClassOrderCourseRelationDO::getCourseId)
                .distinct()
                .collect(Collectors.toList());

        return courseMapper.selectBatchIds(courseIds);
    }

    @Override
    public List<WbClassCourseDO> getPurchasedCoursesByUserId(Long userId) {
        // 优先使用新的课程用户关联服务
        try {
            List<WbClassCourseDO> courses = courseUserRelationService.getCoursesByUserId(userId);
            if (!courses.isEmpty()) {
                return courses;
            }
        } catch (Exception e) {
            log.warn("使用新的课程用户关联服务失败，回退到旧方式: userId={}", userId, e);
        }

        // 回退到旧的订单关联方式
        // 查询用户已支付且未退款的订单
        List<WbClassCourseOrderDO> paidOrders = orderMapper.selectList(
                new LambdaQueryWrapperX<WbClassCourseOrderDO>()
                        .eq(WbClassCourseOrderDO::getUserId, userId)
                        .eq(WbClassCourseOrderDO::getPayStatus, PayStatusEnum.PAID.getStatus())
                        .ne(WbClassCourseOrderDO::getRefundStatus, RefundStatusEnum.REFUNDED.getStatus())
        );

        if (CollUtil.isEmpty(paidOrders)) {
            return new ArrayList<>();
        }

        List<Long> orderIds = paidOrders.stream()
                .map(WbClassCourseOrderDO::getId)
                .collect(Collectors.toList());

        return getCoursesByOrderIds(orderIds);
    }

    @Override
    public boolean hasUserPurchasedCourse(Long userId, Long courseId) {
        // 优先使用新的课程用户关联服务
        try {
            return courseUserRelationService.hasUserCourse(userId, courseId);
        } catch (Exception e) {
            log.warn("使用新的课程用户关联服务失败，回退到旧方式: userId={}, courseId={}", userId, courseId, e);
        }

        // 回退到旧的订单关联方式
        List<WbClassCourseDO> purchasedCourses = getPurchasedCoursesByUserId(userId);
        return purchasedCourses.stream()
                .anyMatch(course -> course.getId().equals(courseId));
    }

    @Override
    public List<WbClassOrderCourseRelationDO> getRelationsByOrderId(Long orderId) {
        return relationMapper.selectListByOrderId(orderId);
    }

    @Override
    public List<WbClassOrderCourseRelationDO> getRelationsByCourseId(Long courseId) {
        return relationMapper.selectListByCourseId(courseId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRelationsByOrderId(Long orderId) {
        relationMapper.deleteByOrderId(orderId);
        log.info("删除订单的所有课程关联关系: orderId={}", orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRelationsByCourseId(Long courseId) {
        relationMapper.deleteByCourseId(courseId);
        log.info("删除课程的所有订单关联关系: courseId={}", courseId);
    }

}
