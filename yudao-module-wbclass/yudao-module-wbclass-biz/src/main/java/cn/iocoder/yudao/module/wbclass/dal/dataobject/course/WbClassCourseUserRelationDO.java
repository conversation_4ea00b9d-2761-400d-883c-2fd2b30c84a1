package cn.iocoder.yudao.module.wbclass.dal.dataobject.course;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 课程用户关联 DO
 * 
 * 用于管理课程与用户的直接关联关系，记录用户拥有的课程及获得方式
 *
 * <AUTHOR>
 */
@TableName("edusys_wbclass_course_user_relation")
@KeySequence("edusys_wbclass_course_user_relation_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WbClassCourseUserRelationDO extends BaseDO {

    /**
     * 关联ID
     */
    @TableId
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 课程名称（冗余字段，便于查询）
     */
    private String courseName;

    /**
     * 获得方式：1-购买，2-赠送，3-活动，4-管理员分配，5-其他
     */
    private Integer acquireType;

    /**
     * 获得时间
     */
    private LocalDateTime acquireTime;

    /**
     * 关联的订单ID（如果是通过购买获得）
     */
    private Long orderId;

    /**
     * 关联的SKU ID（如果是通过购买获得）
     */
    private Long skuId;

    /**
     * 关联的产品ID（如果是通过购买获得）
     */
    private Long productId;

    /**
     * 过期时间（NULL表示永久有效）
     */
    private LocalDateTime expireTime;

    /**
     * 状态：1-有效，2-已过期，3-已取消，4-已删除
     */
    private Integer status;

    /**
     * 备注信息
     */
    private String remark;

}
