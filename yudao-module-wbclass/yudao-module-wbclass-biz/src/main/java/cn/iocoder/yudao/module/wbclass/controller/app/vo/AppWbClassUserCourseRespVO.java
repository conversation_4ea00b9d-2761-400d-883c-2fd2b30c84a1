package cn.iocoder.yudao.module.wbclass.controller.app.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户课程 Response VO
 *
 * <AUTHOR>
 */
@ApiModel("用户端 - 用户课程 Response VO")
@Data
public class AppWbClassUserCourseRespVO {

    @ApiModelProperty(value = "课程ID", example = "1024")
    private Long courseId;

    @ApiModelProperty(value = "课程名称", example = "Java基础课程")
    private String courseName;

    @ApiModelProperty(value = "课程封面图片", example = "https://example.com/cover.jpg")
    private String coverUrl;

    @ApiModelProperty(value = "课程描述", example = "这是一门Java基础课程")
    private String description;

    @ApiModelProperty(value = "获得方式：1-购买，2-赠送，3-活动，4-管理员分配，5-其他", example = "1")
    private Integer acquireType;

    @ApiModelProperty(value = "获得方式名称", example = "购买")
    private String acquireTypeName;

    @ApiModelProperty(value = "获得时间", example = "2024-01-01 12:00:00")
    private LocalDateTime acquireTime;

    @ApiModelProperty(value = "过期时间", example = "2024-12-31 23:59:59")
    private LocalDateTime expireTime;

    @ApiModelProperty(value = "是否永久有效", example = "true")
    private Boolean isPermanent;

    @ApiModelProperty(value = "状态：1-有效，2-已过期，3-已取消，4-已删除", example = "1")
    private Integer status;

    @ApiModelProperty(value = "状态名称", example = "有效")
    private String statusName;

}
