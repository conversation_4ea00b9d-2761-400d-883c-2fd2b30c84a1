package cn.iocoder.yudao.module.wbclass.controller.admin.vo.course;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 课程用户关联 Response VO
 *
 * <AUTHOR>
 */
@ApiModel("管理后台 - 课程用户关联 Response VO")
@Data
public class WbClassCourseUserRelationRespVO {

    @ApiModelProperty(value = "关联ID", example = "1024")
    private Long id;

    @ApiModelProperty(value = "用户ID", example = "1024")
    private Long userId;

    @ApiModelProperty(value = "课程ID", example = "1024")
    private Long courseId;

    @ApiModelProperty(value = "课程名称", example = "Java基础课程")
    private String courseName;

    @ApiModelProperty(value = "获得方式：1-购买，2-赠送，3-活动，4-管理员分配，5-其他", example = "1")
    private Integer acquireType;

    @ApiModelProperty(value = "获得时间", example = "2024-01-01 12:00:00")
    private LocalDateTime acquireTime;

    @ApiModelProperty(value = "关联的订单ID", example = "1024")
    private Long orderId;

    @ApiModelProperty(value = "关联的SKU ID", example = "1024")
    private Long skuId;

    @ApiModelProperty(value = "关联的产品ID", example = "1024")
    private Long productId;

    @ApiModelProperty(value = "过期时间", example = "2024-12-31 23:59:59")
    private LocalDateTime expireTime;

    @ApiModelProperty(value = "状态：1-有效，2-已过期，3-已取消，4-已删除", example = "1")
    private Integer status;

    @ApiModelProperty(value = "备注信息", example = "通过订单购买获得")
    private String remark;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private LocalDateTime createTime;

}
