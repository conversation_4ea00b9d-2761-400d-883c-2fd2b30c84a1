package cn.iocoder.yudao.module.wbclass.enums;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 用户课程状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum UserCourseStatusEnum implements IntArrayValuable {

    VALID(1, "有效"),
    EXPIRED(2, "已过期"),
    CANCELLED(3, "已取消"),
    DELETED(4, "已删除");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(UserCourseStatusEnum::getStatus).toArray();

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static UserCourseStatusEnum valueOf(Integer status) {
        return Arrays.stream(values()).filter(item -> item.getStatus().equals(status)).findFirst().orElse(null);
    }

}
