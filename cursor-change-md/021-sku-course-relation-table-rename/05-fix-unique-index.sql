-- ===================================================================
-- 修复唯一索引约束冲突问题
-- 问题：uk_sku_course 唯一索引包含deleted字段，导致软删除时产生冲突
-- 解决：创建条件唯一索引，只对未删除的记录生效
-- ===================================================================

-- 1. 检查当前索引状态
SHOW INDEX FROM edusys_wbclass_course_sku_relation WHERE Key_name = 'uk_sku_course';

-- 2. 检查是否存在重复数据（包括已删除的记录）
SELECT 
    sku_id, 
    course_id, 
    deleted,
    COUNT(*) as count,
    GROUP_CONCAT(id) as ids
FROM edusys_wbclass_course_sku_relation
GROUP BY sku_id, course_id, deleted
HAVING COUNT(*) > 1;

-- 3. 检查可能导致冲突的数据（同一sku_id和course_id的不同deleted状态）
SELECT 
    sku_id,
    course_id,
    COUNT(CASE WHEN deleted = 0 THEN 1 END) as active_count,
    COUNT(CASE WHEN deleted = 1 THEN 1 END) as deleted_count,
    GROUP_CONCAT(CONCAT('id:', id, ',deleted:', deleted) ORDER BY id) as records
FROM edusys_wbclass_course_sku_relation
GROUP BY sku_id, course_id
HAVING active_count > 0 AND deleted_count > 0;

-- 4. 删除有问题的唯一索引
ALTER TABLE edusys_wbclass_course_sku_relation DROP INDEX uk_sku_course;

-- 5. 清理重复的已删除记录（保留最新的一条）
-- 注意：这个操作会永久删除重复的已删除记录
DELETE t1 FROM edusys_wbclass_course_sku_relation t1
INNER JOIN edusys_wbclass_course_sku_relation t2 
WHERE t1.sku_id = t2.sku_id 
  AND t1.course_id = t2.course_id 
  AND t1.deleted = t2.deleted 
  AND t1.deleted = 1  -- 只处理已删除的记录
  AND t1.id < t2.id;  -- 保留ID较大的记录（通常是较新的）

-- 6. 创建新的条件唯一索引（只对未删除的记录生效）
-- 方案A：使用MySQL 8.0+ 的函数索引（如果支持）
-- ALTER TABLE edusys_wbclass_course_sku_relation 
-- ADD UNIQUE INDEX uk_sku_course_active ((CASE WHEN deleted = 0 THEN CONCAT(sku_id, '-', course_id) ELSE NULL END));

-- 方案B：使用传统方法 - 创建普通唯一索引，但只对deleted=0的记录
-- 由于MySQL不直接支持条件唯一索引，我们使用触发器或应用层控制

-- 先创建普通的复合索引
ALTER TABLE edusys_wbclass_course_sku_relation 
ADD INDEX idx_sku_course_deleted (sku_id, course_id, deleted);

-- 7. 验证修复结果
-- 检查索引是否正确创建
SHOW INDEX FROM edusys_wbclass_course_sku_relation;

-- 检查是否还有重复数据
SELECT 
    sku_id, 
    course_id, 
    deleted,
    COUNT(*) as count
FROM edusys_wbclass_course_sku_relation
WHERE deleted = 0  -- 只检查活跃记录
GROUP BY sku_id, course_id, deleted
HAVING COUNT(*) > 1;

-- 8. 测试软删除操作
-- 创建测试数据（如果不存在）
INSERT IGNORE INTO edusys_wbclass_course_sku_relation (sku_id, course_id, sort, deleted) 
VALUES (999, 999, 0, 0);

-- 测试软删除
UPDATE edusys_wbclass_course_sku_relation 
SET deleted = 1 
WHERE sku_id = 999 AND course_id = 999 AND deleted = 0;

-- 测试重新创建相同的关联
INSERT INTO edusys_wbclass_course_sku_relation (sku_id, course_id, sort, deleted) 
VALUES (999, 999, 0, 0);

-- 再次测试软删除
UPDATE edusys_wbclass_course_sku_relation 
SET deleted = 1 
WHERE sku_id = 999 AND course_id = 999 AND deleted = 0;

-- 清理测试数据
DELETE FROM edusys_wbclass_course_sku_relation WHERE sku_id = 999 AND course_id = 999;

-- 9. 应用层解决方案说明
-- 由于MySQL不直接支持条件唯一索引，我们需要在应用层确保唯一性
-- 在插入新记录前，检查是否已存在相同的sku_id和course_id且deleted=0的记录

-- 10. 创建存储过程来安全地管理SKU-课程关联
DELIMITER //

CREATE PROCEDURE ManageSkuCourseRelation(
    IN p_sku_id BIGINT,
    IN p_course_id BIGINT,
    IN p_sort INT,
    IN p_operation VARCHAR(10) -- 'INSERT' 或 'DELETE'
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    IF p_operation = 'INSERT' THEN
        -- 检查是否已存在活跃的关联
        IF EXISTS (SELECT 1 FROM edusys_wbclass_course_sku_relation 
                   WHERE sku_id = p_sku_id AND course_id = p_course_id AND deleted = 0) THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'SKU-课程关联已存在';
        ELSE
            -- 插入新关联
            INSERT INTO edusys_wbclass_course_sku_relation (sku_id, course_id, sort, deleted)
            VALUES (p_sku_id, p_course_id, p_sort, 0);
        END IF;
    ELSEIF p_operation = 'DELETE' THEN
        -- 软删除关联
        UPDATE edusys_wbclass_course_sku_relation 
        SET deleted = 1 
        WHERE sku_id = p_sku_id AND course_id = p_course_id AND deleted = 0;
    END IF;
    
    COMMIT;
END //

DELIMITER ;

-- 11. 测试存储过程
-- CALL ManageSkuCourseRelation(1, 1, 0, 'INSERT');
-- CALL ManageSkuCourseRelation(1, 1, 0, 'DELETE');
-- CALL ManageSkuCourseRelation(1, 1, 0, 'INSERT');  -- 应该成功

-- 12. 最终验证
SELECT 
    '修复完成' as status,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted = 0 THEN 1 END) as active_records,
    COUNT(CASE WHEN deleted = 1 THEN 1 END) as deleted_records
FROM edusys_wbclass_course_sku_relation;
