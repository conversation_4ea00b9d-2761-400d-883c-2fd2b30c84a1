# 唯一索引冲突问题修复

## 问题描述

在保存SKU课程关联时出现以下错误：

```
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '3-8-\x01' for key 'edusys_wbclass_course_sku_relation.uk_sku_course'
### SQL: UPDATE edusys_wbclass_course_sku_relation SET deleted=1   WHERE  deleted=0  AND (sku_id = ?)
```

## 问题根因

1. **唯一索引设计问题**：我们创建的唯一索引 `uk_sku_course` 包含了 `deleted` 字段：
   ```sql
   UNIQUE KEY `uk_sku_course` (`sku_id`, `course_id`, `deleted`)
   ```

2. **软删除冲突**：当执行软删除操作时，将 `deleted` 从 0 改为 1，如果已经存在相同 `sku_id` 和 `course_id` 但 `deleted=1` 的记录，就会产生唯一键冲突。

3. **场景重现**：
   - 存在记录：(sku_id=3, course_id=8, deleted=1)
   - 要更新记录：(sku_id=3, course_id=8, deleted=0) → (sku_id=3, course_id=8, deleted=1)
   - 结果：违反唯一约束

## 解决方案

### 1. 立即修复（快速解决）

执行快速修复脚本：`06-quick-fix.sql`

```sql
-- 删除有问题的唯一索引
ALTER TABLE edusys_wbclass_course_sku_relation DROP INDEX uk_sku_course;

-- 清理重复的已删除记录
DELETE t1 FROM edusys_wbclass_course_sku_relation t1
INNER JOIN edusys_wbclass_course_sku_relation t2 
WHERE t1.sku_id = t2.sku_id 
  AND t1.course_id = t2.course_id 
  AND t1.deleted = t2.deleted 
  AND t1.deleted = 1
  AND t1.id < t2.id;

-- 创建新的普通索引
ALTER TABLE edusys_wbclass_course_sku_relation 
ADD INDEX idx_sku_course_active (sku_id, course_id, deleted);
```

### 2. 代码层面修复

已修改 `WbClassCourseProductSkuServiceImpl.java` 中的 `saveSkuCourseRelations` 方法，添加重复检查：

```java
// 检查是否已存在活跃的关联关系，避免重复插入
List<WbClassCourseSkuRelationDO> existingRelations = relationMapper.selectList(
    new LambdaQueryWrapperX<WbClassCourseSkuRelationDO>()
        .eq(WbClassCourseSkuRelationDO::getSkuId, skuId)
        .eq(WbClassCourseSkuRelationDO::getCourseId, courseId)
        .eq(WbClassCourseSkuRelationDO::getDeleted, false)
);

if (CollUtil.isEmpty(existingRelations)) {
    // 不存在活跃关联，可以插入新记录
    // ... 插入逻辑
}
```

### 3. 长期解决方案

对于软删除场景，推荐的唯一性约束设计：

#### 方案A：应用层控制（推荐）
- 删除数据库层面的唯一约束
- 在应用层确保业务唯一性
- 只对 `deleted=0` 的记录进行唯一性检查

#### 方案B：使用复合字段
```sql
-- 创建一个计算字段，deleted=0时使用实际值，deleted=1时使用NULL
ALTER TABLE edusys_wbclass_course_sku_relation 
ADD COLUMN unique_key VARCHAR(50) GENERATED ALWAYS AS (
    CASE WHEN deleted = 0 THEN CONCAT(sku_id, '-', course_id) ELSE NULL END
) STORED;

-- 在计算字段上创建唯一索引
ALTER TABLE edusys_wbclass_course_sku_relation 
ADD UNIQUE INDEX uk_sku_course_unique (unique_key);
```

#### 方案C：使用触发器
创建触发器在插入/更新时检查唯一性。

## 修复步骤

### 立即执行（解决当前错误）

1. **执行快速修复脚本**：
   ```bash
   mysql -u username -p database_name < 06-quick-fix.sql
   ```

2. **重启应用**：
   ```bash
   systemctl restart your-app-service
   ```

3. **验证修复**：
   - 测试SKU课程关联的保存功能
   - 确认不再出现DuplicateKeyException

### 后续优化（可选）

1. **执行完整修复脚本**：
   ```bash
   mysql -u username -p database_name < 05-fix-unique-index.sql
   ```

2. **监控应用日志**：
   确认没有其他相关错误

## 验证方法

### 1. 数据库验证
```sql
-- 检查索引状态
SHOW INDEX FROM edusys_wbclass_course_sku_relation;

-- 检查重复数据
SELECT sku_id, course_id, COUNT(*) 
FROM edusys_wbclass_course_sku_relation 
WHERE deleted = 0 
GROUP BY sku_id, course_id 
HAVING COUNT(*) > 1;
```

### 2. 功能验证
- 打开SKU管理页面
- 测试添加课程关联
- 测试删除课程关联
- 测试重新添加相同的课程关联

## 预防措施

1. **设计原则**：
   - 软删除场景下，唯一约束不应包含 `deleted` 字段
   - 业务唯一性应在应用层控制

2. **代码规范**：
   - 在插入前检查是否存在活跃记录
   - 使用事务确保数据一致性

3. **测试覆盖**：
   - 添加单元测试覆盖重复插入场景
   - 添加集成测试验证软删除和重新插入

## 相关文件

- `06-quick-fix.sql` - 快速修复脚本
- `05-fix-unique-index.sql` - 完整修复脚本
- `WbClassCourseProductSkuServiceImpl.java` - 修改后的Service实现

## 注意事项

1. **数据备份**：执行修复前请备份相关数据
2. **停机时间**：建议在低峰期执行修复
3. **回滚准备**：准备回滚方案以防出现问题
