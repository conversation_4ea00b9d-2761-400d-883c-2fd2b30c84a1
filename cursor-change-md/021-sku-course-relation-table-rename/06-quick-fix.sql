-- ===================================================================
-- 快速修复唯一索引冲突问题
-- 立即解决当前的DuplicateKeyException错误
-- ===================================================================

-- 1. 删除有问题的唯一索引
ALTER TABLE edusys_wbclass_course_sku_relation DROP INDEX uk_sku_course;

-- 2. 检查并清理重复的已删除记录
-- 查看当前重复情况
SELECT 
    sku_id, 
    course_id, 
    deleted,
    COUNT(*) as count,
    GROUP_CONCAT(id ORDER BY id) as ids
FROM edusys_wbclass_course_sku_relation
GROUP BY sku_id, course_id, deleted
HAVING COUNT(*) > 1;

-- 3. 删除重复的已删除记录（保留最新的）
DELETE t1 FROM edusys_wbclass_course_sku_relation t1
INNER JOIN edusys_wbclass_course_sku_relation t2 
WHERE t1.sku_id = t2.sku_id 
  AND t1.course_id = t2.course_id 
  AND t1.deleted = t2.deleted 
  AND t1.deleted = 1  -- 只处理已删除的记录
  AND t1.id < t2.id;  -- 保留ID较大的记录

-- 4. 创建新的索引（不包含deleted字段的唯一约束）
-- 只为活跃记录创建普通索引
ALTER TABLE edusys_wbclass_course_sku_relation 
ADD INDEX idx_sku_course_active (sku_id, course_id, deleted);

-- 5. 验证修复
-- 检查索引
SHOW INDEX FROM edusys_wbclass_course_sku_relation;

-- 检查是否还有重复的活跃记录
SELECT 
    sku_id, 
    course_id,
    COUNT(*) as count
FROM edusys_wbclass_course_sku_relation
WHERE deleted = 0
GROUP BY sku_id, course_id
HAVING COUNT(*) > 1;

-- 6. 测试操作（可选）
-- 如果需要测试，可以执行以下语句
/*
-- 插入测试数据
INSERT INTO edusys_wbclass_course_sku_relation (sku_id, course_id, sort, deleted) 
VALUES (9999, 9999, 0, 0);

-- 测试软删除
UPDATE edusys_wbclass_course_sku_relation 
SET deleted = 1 
WHERE sku_id = 9999 AND course_id = 9999 AND deleted = 0;

-- 测试重新插入
INSERT INTO edusys_wbclass_course_sku_relation (sku_id, course_id, sort, deleted) 
VALUES (9999, 9999, 0, 0);

-- 再次测试软删除
UPDATE edusys_wbclass_course_sku_relation 
SET deleted = 1 
WHERE sku_id = 9999 AND course_id = 9999 AND deleted = 0;

-- 清理测试数据
DELETE FROM edusys_wbclass_course_sku_relation WHERE sku_id = 9999;
*/
